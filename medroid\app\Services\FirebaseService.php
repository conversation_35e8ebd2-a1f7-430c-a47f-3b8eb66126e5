<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FirebaseService
{
    private $accessToken = null;
    private $tokenExpiry = null;

    /**
     * Send a notification to a device token using FCM v1 API
     *
     * @param string $token
     * @param string $title
     * @param string $body
     * @param array $data
     * @return bool
     */
    public function sendNotification($token, $title, $body, $data = [])
    {
        try {
            Log::info('Firebase sendNotification called', [
                'token_length' => strlen($token),
                'title' => $title,
                'body' => $body,
                'data_keys' => array_keys($data),
            ]);

            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                Log::error('Failed to get Firebase access token');
                return false;
            }

            $projectId = config('services.firebase.project_id');
            if (empty($projectId)) {
                Log::error('Firebase project ID is not configured');
                return false;
            }

            // FCM v1 API payload structure
            $payload = [
                'message' => [
                    'token' => $token,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                    ],
                    'data' => array_map('strval', $data), // FCM v1 requires all data values to be strings
                ]
            ];

            // Add Android-specific configuration if needed
            if ($data['type'] === 'video_call') {
                $payload['message']['android'] = [
                    'priority' => 'high',
                    'notification' => [
                        'channel_id' => 'video_call_channel',
                        'sound' => 'call_ringtone',
                    ],
                ];
            } else {
                $payload['message']['android'] = [
                    'priority' => 'high',
                    'notification' => [
                        'channel_id' => 'high_importance_channel',
                        'sound' => 'default',
                    ],
                ];
            }

            // Add iOS-specific configuration
            $payload['message']['apns'] = [
                'payload' => [
                    'aps' => [
                        'alert' => [
                            'title' => $title,
                            'body' => $body,
                        ],
                        'sound' => $data['type'] === 'video_call' ? 'call_ringtone.caf' : 'default',
                        'badge' => 1,
                        'category' => $data['type'] === 'video_call' ? 'INCOMING_CALL' : 'MESSAGE',
                        'content-available' => 1,
                        'mutable-content' => 1,
                    ]
                ]
            ];

            // FCM v1 API endpoint
            $url = "https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send";

            Log::info('Sending FCM v1 request', [
                'url' => $url,
                'payload_keys' => array_keys($payload),
                'notification_title' => $payload['message']['notification']['title'] ?? null,
                'token_length' => strlen($token),
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->post($url, $payload);

            Log::info('FCM response received', [
                'status_code' => $response->status(),
                'response_body' => $response->body(),
                'is_successful' => $response->successful(),
                'token_length' => strlen($token),
            ]);

            if ($response->successful()) {
                Log::info('Push notification sent to Flutter app', [
                    'token' => $token,
                    'response' => $response->json()
                ]);
                return true;
            } else {
                $responseBody = $response->body();
                $statusCode = $response->status();

                // Check for specific error types
                $errorMessage = 'Failed to send push notification';
                if ($statusCode === 404) {
                    $errorMessage = 'FCM v1 API endpoint not found. Please check your Firebase project ID.';
                } elseif ($statusCode === 401) {
                    $errorMessage = 'Firebase access token is unauthorized. Please verify your service account configuration.';
                } elseif ($statusCode === 403) {
                    $errorMessage = 'Firebase access token does not have FCM permissions. Please check your service account roles.';
                } elseif ($statusCode === 400) {
                    $errorMessage = 'Invalid FCM v1 API payload. Please check the notification structure.';
                } elseif (strpos($responseBody, 'UNREGISTERED') !== false) {
                    $errorMessage = 'Invalid device token. The token may be expired or invalid.';
                }

                Log::error($errorMessage, [
                    'token' => $token,
                    'response' => $responseBody,
                    'status_code' => $statusCode,
                    'headers' => $response->headers(),
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Error sending Firebase notification to Flutter app', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Send a notification to multiple device tokens
     *
     * @param array $tokens
     * @param string $title
     * @param string $body
     * @param array $data
     * @return bool
     */
    public function sendMulticastNotification(array $tokens, $title, $body, $data = [])
    {
        if (empty($tokens)) {
            return false;
        }

        $success = false;

        foreach ($tokens as $token) {
            $result = $this->sendNotification($token, $title, $body, $data);
            if ($result) {
                $success = true;
            }
        }

        return $success;
    }

    /**
     * Get OAuth 2.0 access token for FCM v1 API
     *
     * @return string|null
     */
    private function getAccessToken()
    {
        // Check if we have a valid cached token
        if ($this->accessToken && $this->tokenExpiry && now()->isBefore($this->tokenExpiry)) {
            return $this->accessToken;
        }

        try {
            $serviceAccountPath = config('services.firebase.service_account_path');

            // If no service account path is configured, try to get from storage
            if (empty($serviceAccountPath)) {
                $serviceAccountPath = storage_path('app/firebase-service-account.json');
            }

            if (!file_exists($serviceAccountPath)) {
                Log::error('Firebase service account file not found', [
                    'path' => $serviceAccountPath
                ]);
                return null;
            }

            $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);

            if (!$serviceAccount || !isset($serviceAccount['private_key']) || !isset($serviceAccount['client_email'])) {
                Log::error('Invalid Firebase service account file');
                return null;
            }

            // Create JWT for Google OAuth
            $now = time();
            $expiry = $now + 3600; // 1 hour

            $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
            $payload = json_encode([
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
                'aud' => 'https://oauth2.googleapis.com/token',
                'iat' => $now,
                'exp' => $expiry,
            ]);

            $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
            $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

            $signature = '';
            openssl_sign(
                $base64Header . '.' . $base64Payload,
                $signature,
                $serviceAccount['private_key'],
                'SHA256'
            );

            $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
            $jwt = $base64Header . '.' . $base64Payload . '.' . $base64Signature;

            // Exchange JWT for access token
            $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt,
            ]);

            if ($response->successful()) {
                $tokenData = $response->json();
                $this->accessToken = $tokenData['access_token'];
                $this->tokenExpiry = now()->addSeconds($tokenData['expires_in'] - 60); // 1 minute buffer

                Log::info('Firebase access token obtained successfully');
                return $this->accessToken;
            } else {
                Log::error('Failed to get Firebase access token', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                ]);
                return null;
            }

        } catch (\Exception $e) {
            Log::error('Error getting Firebase access token', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return null;
        }
    }

    /**
     * Test the Firebase connection
     *
     * @return bool
     */
    public function testConnection()
    {
        try {
            $accessToken = $this->getAccessToken();
            if (!$accessToken) {
                return false;
            }

            $projectId = config('services.firebase.project_id');
            if (empty($projectId)) {
                return false;
            }

            // Test connection by trying to send to an invalid token
            // This will validate our access token and project configuration
            $url = "https://fcm.googleapis.com/v1/projects/{$projectId}/messages:send";

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json'
            ])->post($url, [
                'message' => [
                    'token' => 'invalid_test_token',
                    'notification' => [
                        'title' => 'Test',
                        'body' => 'Test',
                    ],
                ]
            ]);

            // Even with an invalid token, if our access token is valid, we'll get a specific error
            // rather than an authentication error
            $responseData = $response->json();

            // Check for authentication success (even if token is invalid)
            return $response->status() !== 401 && $response->status() !== 403;
        } catch (\Exception $e) {
            Log::error('Error testing Firebase connection', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
