import 'dart:io';
import 'dart:typed_data';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:medroid_app/models/notification_model.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'package:medroid_app/utils/constants.dart';
import 'package:medroid_app/widgets/video_call_notification_dialog.dart';

class NotificationService {
  final ApiService _apiService = ApiService();
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Stream controller for notification count
  final ValueNotifier<int> unreadCountNotifier = ValueNotifier<int>(0);

  // Stream for handling notification taps
  final Stream<RemoteMessage> onMessageOpenedApp =
      FirebaseMessaging.onMessageOpenedApp;

  // Callback for showing video call dialog
  Function(Map<String, dynamic>)? onVideoCallReceived;

  // Callback for handling notification navigation
  Function(Map<String, dynamic>)? onNotificationTapped;

  // Initialize the notification service
  Future<void> initialize() async {
    try {
      // Check if Firebase is initialized
      await Firebase.initializeApp();

      // Create notification channels for Android
      await _createNotificationChannels();

      // Request permission for iOS
      if (Platform.isIOS) {
        await _firebaseMessaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
        );
      }

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        // Handle video call notifications specially
        if (message.data['type'] == 'video_call') {
          _handleVideoCallNotification(message);
        } else {
          _showLocalNotification(message);
        }
        // Remove automatic refresh - let UI handle refresh when needed
      });

      // Handle notification taps when app is opened from notification
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        _handleNotificationTap(message.data);
      });

      // Handle notification tap when app is launched from terminated state
      RemoteMessage? initialMessage =
          await FirebaseMessaging.instance.getInitialMessage();
      if (initialMessage != null) {
        // Delay handling to ensure app is fully initialized
        Future.delayed(const Duration(seconds: 1), () {
          _handleNotificationTap(initialMessage.data);
        });
      }

      // Get the token and register it with the backend
      await _getTokenAndRegister();

      // Remove automatic refresh - notifications will be fetched when needed
    } catch (e) {
      // Continue without notifications if Firebase fails
    }
  }

  // Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    if (Platform.isAndroid) {
      try {
        // Initialize local notifications
        const AndroidInitializationSettings initializationSettingsAndroid =
            AndroidInitializationSettings('@mipmap/ic_launcher');

        const InitializationSettings initializationSettings =
            InitializationSettings(android: initializationSettingsAndroid);

        await _localNotifications.initialize(
          initializationSettings,
          onDidReceiveNotificationResponse: (NotificationResponse response) {
            if (response.payload != null && response.payload!.isNotEmpty) {
              try {
                // Parse the payload as key=value pairs separated by &
                final Map<String, dynamic> data = {};
                final pairs = response.payload!.split('&');
                for (final pair in pairs) {
                  final parts = pair.split('=');
                  if (parts.length == 2) {
                    data[parts[0]] = parts[1];
                  }
                }

                // Handle notification actions
                if (response.actionId != null) {
                  _handleNotificationAction(response.actionId!, data);
                } else {
                  _handleNotificationTap(data);
                }
              } catch (e) {
                // Handle error silently
              }
            }
          },
        );

        // Create high importance channel for general notifications
        const AndroidNotificationChannel highImportanceChannel =
            AndroidNotificationChannel(
          'high_importance_channel',
          'High Importance Notifications',
          description: 'This channel is used for important notifications.',
          importance: Importance.high,
          playSound: true,
          sound: RawResourceAndroidNotificationSound('notification'),
        );

        // Create video call channel with custom ringtone
        final AndroidNotificationChannel videoCallChannel =
            AndroidNotificationChannel(
          'video_call_channel',
          'Video Call Notifications',
          description:
              'This channel is used for incoming video call notifications.',
          importance: Importance.max,
          playSound: true,
          sound: const RawResourceAndroidNotificationSound('call_ringtone'),
          enableVibration: true,
          vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
          showBadge: true,
        );

        // Register the channels with the system
        await _localNotifications
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.createNotificationChannel(highImportanceChannel);

        await _localNotifications
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.createNotificationChannel(videoCallChannel);
      } catch (e) {
        // Handle error silently
      }
    }
  }

  // Get the FCM token and register it with the backend
  Future<void> _getTokenAndRegister() async {
    try {
      // Get FCM token with longer expiration (max available)
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _registerDeviceToken(token);
        // Store token locally for persistence
        await _storeFCMTokenLocally(token);
      }

      // Listen for token refresh (happens rarely with proper configuration)
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _registerDeviceToken(newToken);
        _storeFCMTokenLocally(newToken);
      });
    } catch (e) {
      // Handle error silently
    }
  }

  // Store FCM token locally for persistence
  Future<void> _storeFCMTokenLocally(String token) async {
    try {
      // You can use SharedPreferences to store the token
      // This ensures the token persists across app restarts
    } catch (e) {
      // Handle error silently
    }
  }

  // Register the device token with the backend
  Future<void> _registerDeviceToken(String token) async {
    try {
      final deviceInfo = await DeviceInfoService.getDeviceInfo();
      final userAgent = await DeviceInfoService.getUserAgent();

      final payload = {
        'token': token,
        'device_type': DeviceInfoService.getNotificationDeviceType(),
        'user_agent': userAgent,
        'browser': deviceInfo['browser'] ?? 'Unknown',
        'platform': deviceInfo['platform'] ?? 'Unknown',
        'device_model': deviceInfo['device_model'] ?? 'Unknown',
        'device_brand': deviceInfo['device_brand'] ?? 'Unknown',
        'os_version': deviceInfo['os_version'] ?? 'Unknown',
        'app_version': deviceInfo['app_version'] ?? 'Unknown',
      };

      await _apiService.post(
        Constants.deviceTokenEndpoint,
        payload,
      );
    } catch (e) {
      // Handle error silently
    }
  }

  // Handle video call notifications
  void _handleVideoCallNotification(RemoteMessage message) {
    final data = message.data;

    // For video calls, prioritize full-screen incoming call with ringtone
    _showFullScreenIncomingCall(data);

    // Show enhanced local notification with ringtone for video calls
    _showVideoCallNotification(message);
  }

  // Show full-screen incoming call UI
  void _showFullScreenIncomingCall(Map<String, dynamic> data) {
    // Use callback to show full-screen incoming call
    if (onVideoCallReceived != null) {
      onVideoCallReceived!(data);
    }
  }

  // Handle notification actions (accept/decline from notification)
  void _handleNotificationAction(String actionId, Map<String, dynamic> data) {
    switch (actionId) {
      case 'accept_call':
        _acceptCallFromNotification(data);
        break;
      case 'decline_call':
        _declineCallFromNotification(data);
        break;
      default:
        _handleNotificationTap(data);
        break;
    }
  }

  // Accept call from notification action
  void _acceptCallFromNotification(Map<String, dynamic> data) {
    // Send accept response to backend
    _sendCallResponse(data, 'accepted');

    // Use navigation callback to go to video call
    if (onNotificationTapped != null) {
      data['action'] = 'accept_call';
      onNotificationTapped!(data);
    }
  }

  // Decline call from notification action
  void _declineCallFromNotification(Map<String, dynamic> data) {
    // Send decline response to backend
    _sendCallResponse(data, 'declined');
  }

  // Send call response to backend
  Future<void> _sendCallResponse(
      Map<String, dynamic> data, String response) async {
    try {
      final appointmentId = data['appointment_id'];
      if (appointmentId != null) {
        await _apiService.post(
          'video/call-response/$appointmentId',
          {
            'response': response,
            'session_id': data['session_id'],
          },
        );
      }
    } catch (e) {
      // Handle error silently
    }
  }

  // Show video call dialog with join/decline options
  void _showVideoCallDialog(Map<String, dynamic> data) {
    // Use callback to show video call dialog
    if (onVideoCallReceived != null) {
      onVideoCallReceived!(data);
    }
  }

  // Show a local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      // Create payload string from data
      final payload =
          data.entries.map((entry) => '${entry.key}=${entry.value}').join('&');

      // Determine notification channel based on type
      final String channelId = data['type'] == 'video_call'
          ? 'video_call_channel'
          : 'high_importance_channel';

      // Create notification actions for video calls
      List<AndroidNotificationAction>? actions;
      if (data['type'] == 'video_call') {
        actions = [
          const AndroidNotificationAction(
            'decline_call',
            'Decline',
            icon: DrawableResourceAndroidBitmap('ic_call_end'),
            cancelNotification: true,
          ),
          const AndroidNotificationAction(
            'accept_call',
            'Accept',
            icon: DrawableResourceAndroidBitmap('ic_videocam'),
            cancelNotification: true,
          ),
        ];
      }

      // Show the notification
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        notification.title,
        notification.body,
        NotificationDetails(
          android: AndroidNotificationDetails(
            channelId,
            channelId == 'video_call_channel'
                ? 'Video Call Notifications'
                : 'High Importance Notifications',
            channelDescription: channelId == 'video_call_channel'
                ? 'This channel is used for incoming video call notifications.'
                : 'This channel is used for important notifications.',
            importance: channelId == 'video_call_channel'
                ? Importance.max
                : Importance.high,
            priority: channelId == 'video_call_channel'
                ? Priority.max
                : Priority.high,
            playSound: true,
            enableVibration: true,
            fullScreenIntent: channelId == 'video_call_channel',
            category: channelId == 'video_call_channel'
                ? AndroidNotificationCategory.call
                : AndroidNotificationCategory.message,
            icon: '@mipmap/ic_launcher',
            actions: actions,
            ongoing: data['type'] ==
                'video_call', // Keep video call notifications visible
            autoCancel:
                data['type'] != 'video_call', // Don't auto-cancel video calls
            timeoutAfter: data['type'] == 'video_call'
                ? 30000
                : null, // 30 seconds timeout for calls
          ),
        ),
        payload: payload,
      );
    }
  }

  // Show enhanced video call notification with proper ringtone
  Future<void> _showVideoCallNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification != null) {
      // Create payload string from data
      final payload =
          data.entries.map((entry) => '${entry.key}=${entry.value}').join('&');

      // Create enhanced notification actions for video calls
      final actions = [
        const AndroidNotificationAction(
          'decline_call',
          'Decline',
          cancelNotification: true,
        ),
        const AndroidNotificationAction(
          'accept_call',
          'Accept',
          cancelNotification: true,
        ),
      ];

      // Show the enhanced video call notification
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        notification.title ?? 'Incoming Video Call',
        notification.body ?? 'Dr. ${data['provider_name']} is calling you',
        NotificationDetails(
          android: AndroidNotificationDetails(
            'video_call_channel',
            'Video Call Notifications',
            channelDescription:
                'Incoming video call notifications with ringtone',
            importance: Importance.max,
            priority: Priority.max,
            playSound: true,
            sound: const RawResourceAndroidNotificationSound('call_ringtone'),
            enableVibration: true,
            vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
            fullScreenIntent: true,
            category: AndroidNotificationCategory.call,
            icon: '@mipmap/ic_launcher',
            actions: actions,
            ongoing: true, // Keep notification visible
            autoCancel: false, // Don't auto-cancel
            timeoutAfter: 30000, // 30 seconds timeout
            visibility: NotificationVisibility.public,
            showWhen: true,
            when: DateTime.now().millisecondsSinceEpoch,
          ),
        ),
        payload: payload,
      );
    }
  }

  // Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> data) {
    // Use callback to handle navigation
    if (onNotificationTapped != null) {
      onNotificationTapped!(data);
    } else {
      // Fallback handling
      _handleNotificationTapFallback(data);
    }
  }

  // Fallback notification tap handling
  void _handleNotificationTapFallback(Map<String, dynamic> data) {
    final String type = data['type'] ?? '';

    // Handle different notification types
    switch (type) {
      case 'video_call':
        // Show video call dialog
        _showVideoCallDialog(data);
        break;
      case 'appointment_reminder':
        // Navigate to appointment details
        break;
      case 'appointment_booked':
        // Navigate to appointment details
        break;
      case 'appointment_cancelled':
        // Navigate to appointment list
        break;
      default:
        // Navigate to notification list
        break;
    }
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications(
      {int limit = 20, int offset = 0}) async {
    try {
      final response = await _apiService.get(
        '${Constants.notificationsEndpoint}?limit=$limit&offset=$offset',
      );

      final List<NotificationModel> notifications = [];
      if (response != null && response['notifications'] != null) {
        for (final item in response['notifications']) {
          notifications.add(NotificationModel.fromJson(item));
        }
      }

      return notifications;
    } catch (e) {
      return [];
    }
  }

  // Get unread notification count
  Future<int> getUnreadCount() async {
    try {
      final response =
          await _apiService.get(Constants.notificationsUnreadCountEndpoint);
      final int count = response != null ? response['unread_count'] ?? 0 : 0;
      unreadCountNotifier.value = count;
      return count;
    } catch (e) {
      return 0;
    }
  }

  // Refresh unread count
  Future<void> _refreshUnreadCount() async {
    await getUnreadCount();
  }

  // Mark a notification as read
  Future<bool> markAsRead(int notificationId) async {
    try {
      await _apiService
          .post('${Constants.notificationsEndpoint}/$notificationId/read', {});
      await _refreshUnreadCount();
      return true;
    } catch (e) {
      return false;
    }
  }

  // Mark all notifications as read
  Future<bool> markAllAsRead() async {
    try {
      await _apiService.post('${Constants.notificationsEndpoint}/read-all', {});
      unreadCountNotifier.value = 0;
      return true;
    } catch (e) {
      return false;
    }
  }
}
