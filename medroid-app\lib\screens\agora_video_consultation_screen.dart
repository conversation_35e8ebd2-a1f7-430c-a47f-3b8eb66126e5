import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:medroid_app/services/agora_service_factory.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/video_consultation_service.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'dart:async';

class AgoraVideoConsultationScreen extends StatefulWidget {
  final String appointmentId;
  final bool isProvider;
  final String userName;

  const AgoraVideoConsultationScreen({
    Key? key,
    required this.appointmentId,
    required this.isProvider,
    required this.userName,
  }) : super(key: key);

  @override
  State<AgoraVideoConsultationScreen> createState() =>
      _AgoraVideoConsultationScreenState();
}

class _AgoraVideoConsultationScreenState
    extends State<AgoraVideoConsultationScreen> {
  late final VideoConsultationService _videoService;
  late final dynamic _agoraService; // Can be AgoraService or AgoraWebService
  late final ApiService _apiService;

  // State variables
  bool _isAgoraInitialized = false;
  bool _localAudioMuted = false;
  bool _localVideoMuted = false;
  bool _isEndingCall = false;
  int? _remoteUid;
  int? _localUid; // Add local UID tracking

  // Control button loading states
  bool _isTogglingAudio = false;
  bool _isTogglingVideo = false;
  bool _isSwitchingCamera = false;

  // Connection status (matching web version)
  String _connectionStatus =
      'disconnected'; // disconnected, connecting, connected, waiting
  bool _isConnecting = false;
  String? _connectionError;

  // Timer for periodic checks
  Timer? _periodicTimer;

  // Flag to ensure initialization happens only once
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    _apiService = ApiService();
    _agoraService = AgoraServiceFactory.createAgoraService(_apiService);
    _videoService = VideoConsultationService(_apiService);
    _setupVideoServiceCallbacks();
  }

  /// Setup callbacks for video service
  void _setupVideoServiceCallbacks() {
    _videoService.onConnectionStatusChanged = (status) {
      if (mounted) {
        setState(() {
          _connectionStatus = status;
          _isConnecting = status == 'connecting';
        });
      }
    };

    _videoService.onError = (error) {
      if (mounted) {
        setState(() {
          _connectionError = error;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red,
          ),
        );
      }
    };

    _videoService.onSuccess = (message) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: Colors.green,
          ),
        );
      }
    };

    // Set up Agora event handlers for user join/leave
    _agoraService.onUserJoined = (uid) {
      if (uid != _localUid && mounted) {
        setState(() {
          _remoteUid = uid;
        });

        // Ensure we subscribe to the remote user's streams
        _agoraService.subscribeToRemoteUser(uid);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${widget.isProvider ? "Patient" : "Provider"} has joined the call'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    };

    _agoraService.onUserLeft = (uid) {
      if (uid == _remoteUid && mounted) {
        setState(() {
          _remoteUid = null;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '${widget.isProvider ? "Patient" : "Provider"} has left the call'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    };
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasInitialized) {
      _hasInitialized = true;
      _initializeVideoCall();
    }
  }

  /// Initialize video call (matching web implementation)
  Future<void> _initializeVideoCall() async {
    try {
      setState(() {
        _connectionStatus = 'connecting';
        _isConnecting = true;
      });

      // Get device information
      final deviceInfo = await DeviceInfoService.getDeviceInfo();

      // Use the exact same flow as web app - both provider and patient call getVideoSessionData
      // The backend automatically creates session for provider and joins patient to existing session
      final sessionResponse = await _apiService.getVideoSessionData(
        widget.appointmentId,
        deviceInfo: deviceInfo,
      );

      // If no session exists and user is patient, wait for provider
      if (!sessionResponse['success'] && !widget.isProvider) {
        setState(() {
          _connectionStatus = 'waiting';
          _isConnecting = false;
        });
        _startWaitingForProvider();
        return;
      }

      if (!sessionResponse['success']) {
        throw Exception(
            sessionResponse['message'] ?? 'Failed to get session data');
      }

      // Extract session data - handle both initializeVideoSession and getVideoSessionData responses
      Map<String, dynamic> sessionData;
      if (sessionResponse.containsKey('session_data')) {
        sessionData = sessionResponse['session_data'];
      } else if (sessionResponse.containsKey('sessionData')) {
        sessionData = sessionResponse['sessionData'];
      } else {
        throw Exception('No session data found in response');
      }

      // Initialize Agora service directly (matching Vue approach)
      await _agoraService.initialize();

      // Set session data directly
      _agoraService.channelName = sessionData['channel'];
      _agoraService.uid = int.tryParse(sessionData['uid'].toString()) ?? 0;
      _agoraService.token = sessionData['token'];

      _localUid = _agoraService.uid;

      // Join Agora channel using the session data we already have
      final success = await _agoraService.joinChannelWithSessionData();

      if (success) {
        setState(() {
          _connectionStatus = 'connected';
          _isConnecting = false;
          _isAgoraInitialized = true;
        });

        // Notify backend that we joined Agora channel (matching web flow)
        await _notifyBackendJoined();

        // Wait a moment for connection to stabilize
        await Future.delayed(const Duration(milliseconds: 1000));

        // Check for existing remote users in the channel (matching Vue approach)
        _checkForExistingRemoteUsers();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Connected to video call'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Failed to join Agora channel');
      }
    } catch (e) {
      setState(() {
        _connectionStatus = 'disconnected';
        _isConnecting = false;
      });
      _showError('Failed to initialize video call: $e');
    }
  }

  /// Start waiting for provider (for patients)
  void _startWaitingForProvider() {
    // Poll for session data every 3 seconds
    _periodicTimer = Timer.periodic(const Duration(seconds: 3), (timer) async {
      if (!mounted || _connectionStatus != 'waiting') {
        timer.cancel();
        return;
      }

      try {
        final sessionResponse = await _apiService.getVideoSessionData(
          widget.appointmentId,
          deviceInfo: await DeviceInfoService.getDeviceInfo(),
        );

        if (sessionResponse['success']) {
          timer.cancel();
          _initializeVideoCall();
        }
      } catch (e) {
        // Handle error silently
      }
    });
  }

  /// Notify backend that user joined Agora channel (matching web flow)
  Future<Map<String, dynamic>> _notifyBackendJoined() async {
    try {
      final deviceInfo = await DeviceInfoService.getDeviceInfo();
      final result = await _apiService.joinVideoSession(widget.appointmentId,
          deviceInfo: deviceInfo);

      return result;
    } catch (e) {
      return {
        'success': false,
        'message': 'Exception during backend notification: $e'
      };
    }
  }

  /// Check for existing remote users in the channel (matching Vue approach)
  void _checkForExistingRemoteUsers() {
    if (!kIsWeb && _agoraService.engine != null) {
      // For mobile, we need to manually trigger a check for remote users
      // This is because sometimes the onUserJoined event doesn't fire if users joined before us

      // Start a periodic check for remote users and backend participants
      _periodicTimer =
          Timer.periodic(const Duration(seconds: 2), (timer) async {
        if (!mounted || _remoteUid != null) {
          timer.cancel();
          return;
        }

        try {
          // Check backend for session participants
          final statusResponse =
              await _apiService.getVideoSessionStatus(widget.appointmentId);

          if (statusResponse['success']) {
            if (statusResponse['participants'] != null) {
              final participants = statusResponse['participants'] as List;

              // Look for other participants (not ourselves)
              for (final participant in participants) {
                final participantUid = participant['agora_uid'];
                final participantStatus = participant['status'];

                // If we find a joined participant that's not us, set as remote user
                if (participantUid != _localUid &&
                    participantStatus == 'joined' &&
                    _remoteUid == null) {
                  setState(() {
                    _remoteUid = participantUid;
                  });

                  // Ensure we subscribe to the remote user's streams
                  _agoraService.subscribeToRemoteUser(participantUid);

                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                            '${widget.isProvider ? "Patient" : "Provider"} is connected'),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  }
                  timer.cancel();
                  break;
                }
              }
            }
          }
        } catch (e) {
          // Handle error silently
        }
      });
    }
  }

  /// Show error message
  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _periodicTimer?.cancel();
    try {
      _videoService.dispose();
      _agoraService.dispose();
    } catch (e) {
      // Handle error silently
    }
    super.dispose();
  }

  Widget _buildLocalVideo() {
    if (kIsWeb) {
      // For web, the video is handled by the web service and displayed in HTML containers
      // Show a placeholder that indicates the video is active
      return Container(
        color: Colors.black54,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.videocam,
                color: Colors.white70,
                size: 24,
              ),
              SizedBox(height: 4),
              Text(
                'You (Web)',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Mobile implementation
    if (!_isAgoraInitialized || (_agoraService.engine == null)) {
      return Container(
        color: Colors.black54,
        child: const Center(
          child: CircularProgressIndicator(color: Colors.white),
        ),
      );
    }

    try {
      // Use the actual local UID instead of hardcoded 0
      final localUid = _localUid ?? 0;

      return AgoraVideoView(
        controller: VideoViewController(
          rtcEngine: _agoraService.engine!,
          canvas: VideoCanvas(uid: localUid),
        ),
      );
    } catch (e) {
      return Container(
        color: Colors.black54,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.videocam_off,
                color: Colors.white70,
                size: 24,
              ),
              SizedBox(height: 4),
              Text(
                'Camera unavailable',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildRemoteVideo() {
    if (_remoteUid == null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.person_outline,
                color: Color(0xFFEC4899),
                size: 80,
              ),
              SizedBox(height: 20),
              Text(
                'Waiting for other participant to join...',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 10),
              Text(
                'Your camera and microphone are active',
                style: TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (kIsWeb) {
      // For web, show a placeholder indicating remote user is connected
      // The actual video rendering is handled by AgoraWebService
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.person,
                color: Color(0xFFEC4899),
                size: 80,
              ),
              const SizedBox(height: 20),
              Text(
                '${widget.isProvider ? "Patient" : "Provider"} is connected',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                'Video call is active (Web)\nRemote UID: $_remoteUid',
                style: const TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Mobile implementation
    if (!_isAgoraInitialized ||
        (!kIsWeb &&
            (_agoraService.engine == null ||
                _agoraService.channelName == null))) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: Colors.white),
              SizedBox(height: 20),
              Text(
                'Connecting to remote video...',
                style: TextStyle(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
      );
    }

    // For mobile platforms, use AgoraVideoView
    if (!kIsWeb) {
      try {
        return AgoraVideoView(
          controller: VideoViewController.remote(
            rtcEngine: _agoraService.engine!,
            canvas: VideoCanvas(uid: _remoteUid),
            connection: RtcConnection(channelId: _agoraService.channelName!),
          ),
        );
      } catch (e) {
        return Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 48),
              const SizedBox(height: 16),
              const Text(
                'Unable to display remote video',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.refresh),
                label: const Text('Retry Connection'),
                onPressed: () {
                  if (mounted) {
                    setState(() {
                      // Force rebuild
                    });
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      }
    }

    // For web platforms, the video is handled by HTML containers
    // Show a transparent container since the actual video is rendered by the web service
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.transparent,
    );
  }

  Future<void> _onToggleAudio() async {
    if (!_isAgoraInitialized) {
      _showInitializingMessage();
      return;
    }

    if (mounted) {
      setState(() {
        _isTogglingAudio = true;
      });
    }

    try {
      final enabled = await _agoraService.toggleLocalAudio();

      if (mounted) {
        setState(() {
          _localAudioMuted = !enabled;
          _isTogglingAudio = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? 'Microphone unmuted' : 'Microphone muted'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isTogglingAudio = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error toggling audio: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _onToggleVideo() async {
    if (!_isAgoraInitialized) {
      _showInitializingMessage();
      return;
    }

    if (mounted) {
      setState(() {
        _isTogglingVideo = true;
      });
    }

    try {
      final enabled = await _agoraService.toggleLocalVideo();

      if (mounted) {
        setState(() {
          _localVideoMuted = !enabled;
          _isTogglingVideo = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(enabled ? 'Camera enabled' : 'Camera disabled'),
            duration: const Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isTogglingVideo = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error toggling video: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> _onSwitchCamera() async {
    if (!_isAgoraInitialized) {
      _showInitializingMessage();
      return;
    }

    if (mounted) {
      setState(() {
        _isSwitchingCamera = true;
      });
    }

    try {
      await _agoraService.switchCamera();

      if (mounted) {
        setState(() {
          _isSwitchingCamera = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Camera switched'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isSwitchingCamera = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error switching camera: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showInitializingMessage() {
    if (mounted) {
      Future.microtask(() {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Video call is still initializing. Please wait...'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 2),
            ),
          );
        }
      });
    }
  }

  Future<void> _onEndCall() async {
    debugPrint('Ending call for appointment: ${widget.appointmentId}');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Ending call...'),
          duration: Duration(seconds: 1),
        ),
      );
    }

    setState(() {
      _isEndingCall = true;
    });

    try {
      debugPrint('Leaving Agora channel');
      await _agoraService.leaveChannel();

      // Call the API to end the consultation
      try {
        final apiService = ApiService();
        final result = await apiService.endVideoSession(widget.appointmentId);

        if (result['success']) {
          debugPrint('Consultation ended successfully');
          if (result.containsKey('consultation_duration')) {
            debugPrint(
                'Consultation duration: ${result['consultation_duration']}');
          }
        } else {
          debugPrint('Error ending consultation: ${result['message']}');
        }
      } catch (apiError) {
        debugPrint('API error ending consultation: $apiError');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Call ended successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      if (mounted) {
        debugPrint('Navigating back from video call screen');
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('Error ending call: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error ending call: $e'),
            backgroundColor: Colors.orange,
          ),
        );
        Navigator.pop(context);
      }
    }
  }

  Widget _buildControlButton({
    required IconData icon,
    required Color color,
    required bool isLoading,
    required VoidCallback onPressed,
  }) {
    return isLoading
        ? Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              color: const Color(0xFF2A2A2A),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 6,
                  spreadRadius: 1.0,
                ),
              ],
              border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
            ),
            padding: const EdgeInsets.all(12),
            child: const CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : Container(
            width: 52,
            height: 52,
            decoration: BoxDecoration(
              color: color == Colors.red
                  ? Colors.red.withValues(alpha: 0.3)
                  : const Color(0xFF2A2A2A),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.4),
                  blurRadius: 6,
                  spreadRadius: 1.0,
                ),
              ],
              border: Border.all(color: const Color(0xFF3A3A3A), width: 1),
            ),
            child: IconButton(
              icon: Icon(
                icon,
                color: color,
                size: 26,
              ),
              onPressed: onPressed,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              splashRadius: 26,
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text('Video Consultation with ${widget.userName}'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _onEndCall,
        ),
      ),
      body: Stack(
        children: [
          // Main content
          _connectionStatus == 'waiting'
              ? Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.medical_services,
                          color: Colors.blue,
                          size: 60,
                        ),
                      ),
                      const SizedBox(height: 30),
                      const Text(
                        'Waiting for Provider',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 15),
                      const Text(
                        'Please wait while the provider starts the video consultation',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 30),
                      const CircularProgressIndicator(
                        color: Colors.blue,
                        strokeWidth: 3,
                      ),
                    ],
                  ),
                )
              : !_isAgoraInitialized
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const CircularProgressIndicator(color: Colors.white),
                          const SizedBox(height: 20),
                          Text(
                            _isConnecting
                                ? 'Connecting to video call...'
                                : 'Initializing video call...',
                            style: const TextStyle(
                                color: Colors.white70, fontSize: 16),
                          ),
                        ],
                      ),
                    )
                  : Stack(
                      children: [
                        // Remote video (full screen)
                        Center(child: _buildRemoteVideo()),

                        // Local video (picture-in-picture) - ALWAYS visible when initialized
                        Positioned(
                          right: 20,
                          bottom: 120, // Fixed position above controls
                          width: 100,
                          height: 140,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: Colors.black54,
                              border:
                                  Border.all(color: Colors.white30, width: 1),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 5,
                                  spreadRadius: 1,
                                ),
                              ],
                            ),
                            clipBehavior: Clip.hardEdge,
                            child: _buildLocalVideo(),
                          ),
                        ),

                        // Floating controls - ALWAYS visible when initialized
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 20,
                          child: Center(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 20),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              decoration: BoxDecoration(
                                color: const Color(0xFF1E1E1E),
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    blurRadius: 10,
                                    spreadRadius: 2,
                                  ),
                                ],
                                border: Border.all(
                                    color: const Color(0xFF3A3A3A), width: 1),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _buildControlButton(
                                    icon: _localAudioMuted
                                        ? Icons.mic_off
                                        : Icons.mic,
                                    color: _localAudioMuted
                                        ? Colors.red
                                        : Colors.white,
                                    isLoading: _isTogglingAudio,
                                    onPressed: _onToggleAudio,
                                  ),
                                  const SizedBox(width: 24),
                                  _buildControlButton(
                                    icon: _localVideoMuted
                                        ? Icons.videocam_off
                                        : Icons.videocam,
                                    color: _localVideoMuted
                                        ? Colors.red
                                        : Colors.white,
                                    isLoading: _isTogglingVideo,
                                    onPressed: _onToggleVideo,
                                  ),
                                  const SizedBox(width: 24),
                                  _buildControlButton(
                                    icon: Icons.switch_camera,
                                    color: Colors.white,
                                    isLoading: _isSwitchingCamera,
                                    onPressed: _onSwitchCamera,
                                  ),
                                  const SizedBox(width: 24),
                                  Container(
                                    width: 56,
                                    height: 56,
                                    decoration: BoxDecoration(
                                      color: Colors.red,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color:
                                              Colors.red.withValues(alpha: 0.3),
                                          blurRadius: 8,
                                          spreadRadius: 2,
                                        ),
                                      ],
                                    ),
                                    child: IconButton(
                                      icon: const Icon(
                                        Icons.call_end,
                                        color: Colors.white,
                                        size: 28,
                                      ),
                                      onPressed: _onEndCall,
                                      padding: EdgeInsets.zero,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

          // Call ending indicator
          if (_isEndingCall)
            Container(
              color: Colors.black54,
              width: double.infinity,
              height: double.infinity,
              child: const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 20),
                    Text(
                      'Ending call...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      // Remove bottom navigation bar since floating controls are always visible
      bottomNavigationBar: null,
    );
  }
}
