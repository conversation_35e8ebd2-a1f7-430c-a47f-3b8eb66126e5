import 'package:agora_rtc_engine/agora_rtc_engine.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/device_info_service.dart';
import 'package:medroid_app/utils/env_config.dart';

/// Simplified Agora service for video consultations
class AgoraService {
  final ApiService _apiService;

  // Agora engine instance
  RtcEngine? engine;

  // Session data
  String? channelName;
  int? uid;
  String? token;
  String? appId;

  // Audio/Video state
  bool _isAudioEnabled = true;
  bool _isVideoEnabled = true;

  // Callbacks
  Function(int uid)? onUserJoined;
  Function(int uid)? onUserLeft;
  Function(ConnectionStateType state)? onConnectionStateChanged;
  Function(String error)? onError;

  // Constructor
  AgoraService(this._apiService);

  /// Initialize the Agora engine
  Future<void> initialize() async {
    try {
      // Mobile-only implementation

      // Create RTC engine instance (mobile only)
      engine = createAgoraRtcEngine();

      // Get App ID from environment configuration
      appId = EnvConfig.agoraAppId;

      // Initialize the engine
      await engine!.initialize(
        RtcEngineContext(
          appId: appId!,
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
        ),
      );

      // Register event handlers
      engine!.registerEventHandler(
        RtcEngineEventHandler(
          onJoinChannelSuccess: (RtcConnection connection, int elapsed) {
            // Notify connection state change
            onConnectionStateChanged
                ?.call(ConnectionStateType.connectionStateConnected);
          },
          onUserJoined: (RtcConnection connection, int remoteUid, int elapsed) {
            if (remoteUid != uid) {
              onUserJoined?.call(remoteUid);
            }
          },
          onUserOffline: (
            RtcConnection connection,
            int remoteUid,
            UserOfflineReasonType reason,
          ) {
            if (remoteUid != uid) {
              onUserLeft?.call(remoteUid);
            }
          },
          onRemoteVideoStateChanged: (RtcConnection connection,
              int remoteUid,
              RemoteVideoState state,
              RemoteVideoStateReason reason,
              int elapsed) {
            // This can help detect when remote users start/stop video
            if (state == RemoteVideoState.remoteVideoStateStarting ||
                state == RemoteVideoState.remoteVideoStateDecoding) {
              if (remoteUid != uid) {
                onUserJoined?.call(remoteUid);
              }
            }
          },
          onRemoteAudioStateChanged: (RtcConnection connection,
              int remoteUid,
              RemoteAudioState state,
              RemoteAudioStateReason reason,
              int elapsed) {
            // This can help detect when remote users start/stop audio
            if (state == RemoteAudioState.remoteAudioStateStarting ||
                state == RemoteAudioState.remoteAudioStateDecoding) {
              if (remoteUid != uid) {
                onUserJoined?.call(remoteUid);
              }
            }
          },
          onConnectionStateChanged: (
            RtcConnection connection,
            ConnectionStateType state,
            ConnectionChangedReasonType reason,
          ) {
            onConnectionStateChanged?.call(state);
          },
          onError: (ErrorCodeType err, String msg) {
            onError?.call('$err: $msg');
          },
        ),
      );

      // Enable audio and video
      await engine!.enableVideo();
      await engine!.enableAudio();
      await engine!.setClientRole(role: ClientRoleType.clientRoleBroadcaster);

      // Start local video preview
      await engine!.startPreview();

      // Enable local audio and video publishing
      await engine!.enableLocalAudio(true);
      await engine!.enableLocalVideo(true);

      // Set video configuration for better quality and stability
      await engine!.setVideoEncoderConfiguration(
        const VideoEncoderConfiguration(
          dimensions: VideoDimensions(width: 640, height: 480),
          frameRate: 15,
          bitrate: 400, // Fixed bitrate for stability (400 kbps)
          orientationMode: OrientationMode.orientationModeAdaptive,
          degradationPreference: DegradationPreference.maintainFramerate,
        ),
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Join a video consultation channel
  Future<bool> joinChannel({
    required String appointmentId,
    required bool isProvider,
  }) async {
    try {
      // Mobile-only video consultation

      // Request permissions (mobile only)
      await [Permission.camera, Permission.microphone].request();

      // Get device information
      final deviceInfo = await DeviceInfoService.getDeviceInfo();

      // Get session data from our new simplified API
      final sessionData = await _apiService.getVideoSessionData(appointmentId,
          deviceInfo: deviceInfo);

      if (!sessionData['success']) {
        return false;
      }

      final sessionInfo = sessionData['session_data'];

      // Extract session information
      channelName = sessionInfo['channel'];
      uid = int.tryParse(sessionInfo['uid'].toString()) ?? 0;
      token = sessionInfo['token'];

      // Join the channel
      await engine!.joinChannel(
        token: token!,
        channelId: channelName!,
        uid: uid!,
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          publishCameraTrack: true,
          publishMicrophoneTrack: true,
          autoSubscribeAudio: true,
          autoSubscribeVideo: true,
        ),
      );

      // Ensure local streams are publishing after joining
      await engine!.enableLocalAudio(true);
      await engine!.enableLocalVideo(true);

      // Start preview if not already started
      await engine!.startPreview();

      // Notify connection state change
      onConnectionStateChanged
          ?.call(ConnectionStateType.connectionStateConnected);

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Join channel with pre-existing session data
  Future<bool> joinChannelWithSessionData() async {
    try {
      if (channelName == null || uid == null || token == null) {
        return false;
      }

      // Request permissions (mobile only)
      await [Permission.camera, Permission.microphone].request();

      // Join the channel with existing session data
      await engine!.joinChannel(
        token: token!,
        channelId: channelName!,
        uid: uid!,
        options: const ChannelMediaOptions(
          channelProfile: ChannelProfileType.channelProfileLiveBroadcasting,
          clientRoleType: ClientRoleType.clientRoleBroadcaster,
          publishCameraTrack: true,
          publishMicrophoneTrack: true,
          autoSubscribeAudio: true,
          autoSubscribeVideo: true,
        ),
      );

      // Ensure local streams are publishing after joining
      await engine!.enableLocalAudio(true);
      await engine!.enableLocalVideo(true);

      // Start preview if not already started
      await engine!.startPreview();

      return true;
    } catch (e) {
      return false;
    }
  }

  /// Leave the channel
  Future<void> leaveChannel() async {
    try {
      if (engine != null) {
        await engine!.leaveChannel();
      }

      // Reset session data
      channelName = null;
      uid = null;
      token = null;
    } catch (e) {
      // Handle error silently
    }
  }

  /// Toggle local audio (mute/unmute)
  Future<bool> toggleLocalAudio() async {
    try {
      if (engine == null) {
        return false;
      }

      _isAudioEnabled = !_isAudioEnabled;
      await engine!.enableLocalAudio(_isAudioEnabled);

      return _isAudioEnabled;
    } catch (e) {
      return false;
    }
  }

  /// Toggle local video (enable/disable)
  Future<bool> toggleLocalVideo() async {
    try {
      if (engine == null) {
        return false;
      }

      _isVideoEnabled = !_isVideoEnabled;
      await engine!.enableLocalVideo(_isVideoEnabled);

      return _isVideoEnabled;
    } catch (e) {
      return false;
    }
  }

  /// Switch camera
  Future<void> switchCamera() async {
    try {
      if (engine != null) {
        await engine!.switchCamera();
      }
    } catch (e) {
      // Handle error silently
    }
  }

  /// Manually subscribe to remote user's streams
  Future<void> subscribeToRemoteUser(int remoteUid) async {
    try {
      if (engine != null) {
        // The auto-subscribe should handle this, but we can force it if needed
      }
    } catch (e) {
      // Handle error silently
    }
  }

  /// Dispose the service
  void dispose() {
    try {
      leaveChannel();
    } catch (e) {
      // Handle error silently
    }

    try {
      engine?.release();
      engine = null;
    } catch (e) {
      // Handle error silently
    }

    // Reset all state
    channelName = null;
    uid = null;
    token = null;
    _isAudioEnabled = true;
    _isVideoEnabled = true;
  }

  // Getters for current state
  bool get isAudioEnabled => _isAudioEnabled;
  bool get isVideoEnabled => _isVideoEnabled;
  bool get isConnected => channelName != null && uid != null;
}
