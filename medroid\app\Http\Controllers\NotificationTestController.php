<?php

namespace App\Http\Controllers;

use App\Models\DeviceToken;
use App\Models\User;
use App\Services\NotificationService;
use App\Services\FirebaseService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NotificationTestController extends Controller
{
    protected $notificationService;
    protected $firebaseService;

    public function __construct(NotificationService $notificationService, FirebaseService $firebaseService)
    {
        $this->notificationService = $notificationService;
        $this->firebaseService = $firebaseService;
    }

    /**
     * Test the complete notification flow for a user
     */
    public function testNotificationFlow(Request $request)
    {
        try {
            $user = $request->user();
            
            Log::info('Starting notification flow test', [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);

            $results = [];

            // 1. Check if user has device tokens
            $deviceTokens = DeviceToken::where('user_id', $user->id)->get();
            $results['device_tokens'] = [
                'count' => $deviceTokens->count(),
                'tokens' => $deviceTokens->map(function($token) {
                    return [
                        'id' => $token->id,
                        'device_type' => $token->device_type,
                        'platform' => $token->platform,
                        'device_model' => $token->device_model,
                        'last_used_at' => $token->last_used_at,
                        'token_length' => strlen($token->token),
                        'token_preview' => substr($token->token, 0, 20) . '...',
                    ];
                })->toArray(),
            ];

            if ($deviceTokens->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No device tokens found for user',
                    'results' => $results,
                ]);
            }

            // 2. Test Firebase API key
            $firebaseTest = $this->firebaseService->testConnection();
            $results['firebase_connection'] = $firebaseTest;

            // 3. Send test notification
            $success = $this->notificationService->sendPushNotification(
                $user,
                'Test Video Call',
                'Dr. Test is calling you for your appointment - this is a test from backend',
                'video_call',
                [
                    'appointment_id' => '999',
                    'session_id' => 'test_session_' . time(),
                    'channel_name' => 'test_channel',
                    'provider_name' => 'Dr. Test Backend',
                    'provider_id' => '1',
                    'call_action' => 'incoming_call',
                    'timestamp' => now()->toISOString(),
                ]
            );

            $results['notification_sent'] = $success;

            Log::info('Notification flow test completed', [
                'user_id' => $user->id,
                'results' => $results,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Notification flow test completed',
                'results' => $results,
            ]);

        } catch (\Exception $e) {
            Log::error('Error in notification flow test', [
                'user_id' => $request->user()->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user's device tokens information
     */
    public function getUserDeviceTokens(Request $request)
    {
        try {
            $user = $request->user();
            $deviceTokens = DeviceToken::where('user_id', $user->id)->get();

            return response()->json([
                'success' => true,
                'user_id' => $user->id,
                'user_email' => $user->email,
                'device_tokens_count' => $deviceTokens->count(),
                'device_tokens' => $deviceTokens->map(function($token) {
                    return [
                        'id' => $token->id,
                        'device_type' => $token->device_type,
                        'platform' => $token->platform,
                        'browser' => $token->browser,
                        'device_model' => $token->device_model,
                        'device_brand' => $token->device_brand,
                        'os_version' => $token->os_version,
                        'app_version' => $token->app_version,
                        'user_agent' => $token->user_agent,
                        'last_used_at' => $token->last_used_at,
                        'created_at' => $token->created_at,
                        'token_length' => strlen($token->token),
                        'token_preview' => substr($token->token, 0, 30) . '...',
                    ];
                })->toArray(),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get device tokens: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test Firebase connection
     */
    public function testFirebaseConnection()
    {
        try {
            $result = $this->firebaseService->testConnection();
            
            return response()->json([
                'success' => $result,
                'message' => $result ? 'Firebase connection successful' : 'Firebase connection failed',
                'firebase_api_key_configured' => !empty(config('services.firebase.api_key')),
                'api_key_length' => strlen(config('services.firebase.api_key') ?? ''),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Firebase test failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}