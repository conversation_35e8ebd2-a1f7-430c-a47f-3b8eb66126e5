<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\FirebaseService;

class TestFirebaseConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'firebase:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Firebase FCM connection and configuration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Firebase FCM connection...');

        $firebaseService = new FirebaseService();
        
        // Test connection
        $connectionTest = $firebaseService->testConnection();
        
        if ($connectionTest) {
            $this->info('✅ Firebase connection test passed!');
            
            // Test sending a notification to a test token
            $this->info('Testing notification sending...');
            
            $testToken = $this->ask('Enter a test device token (optional, press Enter to skip)');
            
            if ($testToken) {
                $result = $firebaseService->sendNotification(
                    $testToken,
                    'Test Notification',
                    'This is a test notification from Medroid backend',
                    ['type' => 'test', 'timestamp' => now()->toISOString()]
                );
                
                if ($result) {
                    $this->info('✅ Test notification sent successfully!');
                } else {
                    $this->error('❌ Failed to send test notification');
                }
            }
        } else {
            $this->error('❌ Firebase connection test failed!');
            $this->error('Please check your Firebase configuration:');
            $this->error('1. FIREBASE_PROJECT_ID is set correctly');
            $this->error('2. Firebase service account JSON file exists');
            $this->error('3. Service account has FCM permissions');
        }

        // Show current configuration
        $this->info("\nCurrent Firebase Configuration:");
        $this->table(
            ['Setting', 'Value'],
            [
                ['Project ID', config('services.firebase.project_id') ?: 'Not set'],
                ['Service Account Path', config('services.firebase.service_account_path') ?: 'Not set'],
                ['Service Account File Exists', file_exists(config('services.firebase.service_account_path') ?: storage_path('app/firebase-service-account.json')) ? 'Yes' : 'No'],
            ]
        );

        return Command::SUCCESS;
    }
}
