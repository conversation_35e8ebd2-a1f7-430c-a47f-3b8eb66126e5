import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:medroid_app/services/auth_service.dart';
import 'package:medroid_app/services/api_service.dart';
import 'package:medroid_app/services/notification_service.dart';
import 'package:medroid_app/screens/app_entry_screen.dart';
import 'package:medroid_app/screens/appointments_screen_wrapper.dart';
import 'package:medroid_app/screens/splash_screen.dart';
import 'package:medroid_app/screens/main_navigation.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:flutter/foundation.dart';
import 'package:medroid_app/screens/ai_chat_screen.dart';
import 'package:medroid_app/screens/chat_history_screen.dart';
import 'package:medroid_app/screens/provider_marketplace.dart';
import 'package:medroid_app/screens/provider_dashboard_screen.dart';
import 'package:medroid_app/screens/shopping_cart_screen.dart';
import 'package:medroid_app/screens/orders_screen.dart';
import 'package:medroid_app/screens/product_detail_screen.dart';
import 'package:medroid_app/screens/provider_product_management_screen.dart';
import 'package:medroid_app/models/product.dart';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'firebase_options.dart';
import 'package:medroid_app/widgets/login_button.dart';
import 'package:medroid_app/utils/app_colors.dart';
import 'package:medroid_app/utils/theme.dart';
import 'package:medroid_app/utils/env_config.dart';
import 'package:medroid_app/utils/responsive_utils.dart';
import 'package:medroid_app/widgets/build_version_widget.dart';
import 'package:medroid_app/services/call_navigation_service.dart';
import 'package:medroid_app/screens/incoming_call_screen.dart';
import 'package:medroid_app/widgets/notification_wrapper.dart';

// Background message handler - must be top-level function
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Handle video call notifications in background
  if (message.data['type'] == 'video_call') {
    // The notification will be shown by the system automatically
    // We can add custom logic here if needed
  }
}

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment configuration
  // Use staging environment for testing
  await EnvConfig.init(
    env: 'staging',
  );

  // Initialize Firebase for mobile platforms
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Register background message handler
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  } catch (e) {
    // Continue without Firebase if it fails
  }

  // Initialize Stripe (only for mobile platforms)
  try {
    if (!kIsWeb) {
      Stripe.publishableKey = EnvConfig.stripePublishableKey;
      await Stripe.instance.applySettings();
    }
  } catch (e) {
    // Continue without Stripe if it fails
  }

  runApp(const MedroidApp());
}

class MedroidApp extends StatelessWidget {
  const MedroidApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiRepositoryProvider(
      providers: [
        // Core services
        RepositoryProvider<AuthService>(
          create: (context) => AuthService(),
        ),
        RepositoryProvider<ApiService>(
          create: (context) => ApiService(),
        ),

        // Notification services - use appropriate one based on platform
        RepositoryProvider<NotificationService>(
          create: (context) => NotificationService(),
        ),
      ],
      child: MaterialApp(
        title: 'Medroid',
        debugShowCheckedModeBanner: false,
        themeMode: ThemeMode.light, // Always use light mode
        navigatorKey: CallNavigationService.navigatorKey,
        // Add a builder for responsive design and build version overlay
        builder: (context, child) {
          // Apply a responsive font scale based on screen size
          final mediaQueryData = MediaQuery.of(context);
          final scale = mediaQueryData.size.width <=
                  ResponsiveUtils.mobileBreakpoint
              ? 0.9 // Smaller font for mobile
              : mediaQueryData.size.width <= ResponsiveUtils.tabletBreakpoint
                  ? 1.0 // Normal font for tablet
                  : 1.1; // Larger font for desktop

          final responsiveChild = MediaQuery(
            data: mediaQueryData.copyWith(
              textScaler: TextScaler.linear(scale),
            ),
            child: child!,
          );

          // Add build version overlay and notification wrapper
          return BuildVersionOverlay(
            showFullVersion:
                false, // Mobile only - no need for full version display
            child: NotificationWrapper(
              child: responsiveChild,
            ),
          );
        },
        // Define routes for navigation
        routes: {
          '/': (context) => const SplashScreen(),
          '/main': (context) => const MainNavigation(),
          '/chat': (context) => const AIChatScreen(),
          '/history': (context) => const ChatHistoryScreen(),
          '/chat-history': (context) => const ChatHistoryScreen(),
          '/appointments': (context) => const AppointmentsScreenWrapper(),
          '/providers': (context) => const ProviderMarketplaceScreen(),
          '/provider-dashboard': (context) => const ProviderDashboardScreen(),
          '/profile': (context) =>
              const MainNavigation(initialTabIndex: 4), // Profile tab
          '/shop': (context) =>
              const MainNavigation(initialTabIndex: 3), // Shop tab
        },
        // Define route generator for routes with arguments
        onGenerateRoute: (settings) {
          if (settings.name == '/main') {
            // Extract the arguments
            final args = settings.arguments as Map<String, dynamic>?;
            return MaterialPageRoute(
              builder: (context) {
                // Pass the arguments to the MainNavigation widget
                return MainNavigation(
                  initialTabIndex: args?['tabIndex'] as int? ?? 0,
                  initialConversationId: args?['conversationId'] as String?,
                );
              },
              settings: settings,
            );
          }

          // Shop-related routes
          if (settings.name == '/cart') {
            return MaterialPageRoute(
              builder: (context) => const ShoppingCartScreen(),
              settings: settings,
            );
          }

          if (settings.name == '/orders') {
            return MaterialPageRoute(
              builder: (context) => const OrdersScreen(),
              settings: settings,
            );
          }

          if (settings.name == '/product-detail') {
            final product = settings.arguments as Product?;
            if (product != null) {
              return MaterialPageRoute(
                builder: (context) => ProductDetailScreen(product: product),
                settings: settings,
              );
            }
          }

          if (settings.name == '/provider-products') {
            return MaterialPageRoute(
              builder: (context) => const ProviderProductManagementScreen(),
              settings: settings,
            );
          }

          return null;
        },
        theme: AppTheme.lightTheme,
        // Use initialRoute instead of home to work with named routes
        initialRoute: '/',
      ),
    );
  }
}

// This is a simplified version of the RegisterScreen class with proper indentation
class RegisterScreen extends StatefulWidget {
  final String? reason;

  const RegisterScreen({Key? key, this.reason}) : super(key: key);

  @override
  RegisterScreenState createState() => RegisterScreenState();
}

class RegisterScreenState extends State<RegisterScreen> {
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _dobController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  String _selectedGender = 'prefer_not_to_say';
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _dobController.dispose();
    super.dispose();
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final authService = RepositoryProvider.of<AuthService>(context);
      final success = await authService.register(
        name: _nameController.text,
        email: _emailController.text,
        password: _passwordController.text,
        role: 'patient', // Always register as patient
        gender: _selectedGender,
        dateOfBirth:
            _dobController.text.isNotEmpty ? _dobController.text : null,
      );

      if (success && mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const AppEntryScreen()),
        );
      } else if (mounted) {
        setState(() {
          _errorMessage = 'Registration failed';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Build a gender option widget
  Widget _buildGenderOption(String label, String value) {
    final isSelected = _selectedGender == value;
    return InkWell(
      onTap: () {
        setState(() {
          _selectedGender = value;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color.fromRGBO(23, 195, 178, 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.tealSurge : Colors.grey.shade300,
            width: 1.5,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? AppColors.tealSurge : Colors.grey.shade700,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Register'),
        elevation: 0,
        backgroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.white, Color(0xFFF0F2F5)],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // App logo
                    Center(
                      child: Container(
                        height: 80,
                        width: 80,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [AppColors.tealSurge, AppColors.mintGlow],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromRGBO(23, 195, 178, 0.3),
                              blurRadius: 12,
                              offset: Offset(0, 4),
                            ),
                          ],
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.medical_services,
                            size: 40,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Title
                    Center(
                      child: Text(
                        'Create Your Account',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Center(
                      child: Text(
                        'Join Medroid for personalized healthcare',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Full Name field with enhanced styling
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: const [
                          BoxShadow(
                            color: Color.fromRGBO(23, 195, 178, 0.05),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          labelText: 'Full Name',
                          prefixIcon: const Icon(Icons.person,
                              color: AppColors.tealSurge),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: const BorderSide(
                                color: AppColors.tealSurge, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your name';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Email field with enhanced styling
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: const [
                          BoxShadow(
                            color: Color(0x0AEC4899),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextFormField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          labelText: 'Email',
                          prefixIcon: const Icon(Icons.email,
                              color: AppColors.tealSurge),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: const BorderSide(
                                color: AppColors.tealSurge, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          }
                          // Simple email validation
                          if (!value.contains('@') || !value.contains('.')) {
                            return 'Please enter a valid email';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Password field with enhanced styling
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: const [
                          BoxShadow(
                            color: Color.fromRGBO(23, 195, 178, 0.05),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextFormField(
                        controller: _passwordController,
                        decoration: InputDecoration(
                          labelText: 'Password',
                          prefixIcon: const Icon(Icons.lock,
                              color: AppColors.tealSurge),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: const BorderSide(
                                color: AppColors.tealSurge, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter a password';
                          }
                          if (value.length < 8) {
                            return 'Password must be at least 8 characters';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Confirm Password field with enhanced styling
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: const [
                          BoxShadow(
                            color: Color.fromRGBO(23, 195, 178, 0.05),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextFormField(
                        controller: _confirmPasswordController,
                        decoration: InputDecoration(
                          labelText: 'Confirm Password',
                          prefixIcon: const Icon(Icons.lock_clock,
                              color: AppColors.tealSurge),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: const BorderSide(
                                color: AppColors.tealSurge, width: 2),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                        ),
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please confirm your password';
                          }
                          if (value != _passwordController.text) {
                            return 'Passwords do not match';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Date of Birth field with enhanced styling
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: const [
                          BoxShadow(
                            color: Color.fromRGBO(23, 195, 178, 0.05),
                            blurRadius: 8,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: GestureDetector(
                        onTap: () async {
                          final DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now().subtract(const Duration(
                                days: 365 * 25)), // Default to 25 years ago
                            firstDate: DateTime(1900),
                            lastDate: DateTime.now(),
                            builder: (context, child) {
                              return Theme(
                                data: Theme.of(context).copyWith(
                                  colorScheme: const ColorScheme.light(
                                    primary: AppColors.tealSurge,
                                    onPrimary: Colors.white,
                                    surface: Colors.white,
                                    onSurface: Colors.black,
                                  ),
                                ),
                                child: child!,
                              );
                            },
                          );

                          if (picked != null) {
                            setState(() {
                              _dobController.text =
                                  "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
                            });
                          }
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            controller: _dobController,
                            decoration: InputDecoration(
                              labelText: 'Date of Birth',
                              prefixIcon: const Icon(Icons.calendar_today,
                                  color: AppColors.tealSurge),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16),
                                borderSide:
                                    BorderSide(color: Colors.grey.shade300),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16),
                                borderSide:
                                    BorderSide(color: Colors.grey.shade300),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16),
                                borderSide: const BorderSide(
                                    color: AppColors.tealSurge, width: 2),
                              ),
                              filled: true,
                              fillColor: Colors.white,
                              hintText: 'YYYY-MM-DD',
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),
                    // Gender selection with enhanced styling
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Gender',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildGenderOption('Male', 'male'),
                              _buildGenderOption('Female', 'female'),
                              _buildGenderOption('Other', 'other'),
                              _buildGenderOption(
                                  'Prefer not to say', 'prefer_not_to_say'),
                            ],
                          ),
                        ],
                      ),
                    ),
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    const SizedBox(height: 24),

                    // Enhanced register button
                    _isLoading
                        ? Container(
                            width: double.infinity,
                            height: 56,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            alignment: Alignment.center,
                            child: const SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            ),
                          )
                        : Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: const [
                                BoxShadow(
                                  color: Color.fromRGBO(23, 195, 178, 0.3),
                                  blurRadius: 12,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: LoginButton(
                              onPressed: _register,
                              width: double.infinity,
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Create Account',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Icon(
                                    Icons.arrow_forward_rounded,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ],
                              ),
                            ),
                          ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
